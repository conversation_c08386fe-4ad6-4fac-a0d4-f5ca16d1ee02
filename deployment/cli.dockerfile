# Multi-stage Dockerfile for the Queue Worker service
# This container runs <PERSON><PERSON>'s queue worker to process background jobs
# It's optimized for running queue processing tasks in a separate container

# Manifest/Metadata Labels
LABEL org.opencontainers.image.title="Linanok Queue Worker"
LABEL org.opencontainers.image.description="<PERSON><PERSON> queue worker container for Linanok platform - handles background job processing"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.created="2025-07-31"
LABEL org.opencontainers.image.source="https://github.com/linanok/linanok"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.vendor="Linanok"
LABEL org.opencontainers.image.authors="Linanok Team"
LABEL org.opencontainers.image.documentation="https://github.com/linanok/linanok/blob/main/README.md"
LABEL app.name="linanok"
LABEL app.component="queue-worker"
LABEL app.version="1.0.0"
LABEL app.framework="laravel"
LABEL app.php.version="8.4"

# Stage 1: Build Dependencies
# This stage handles dependency installation and build tools
FROM php:8.4-cli AS build_dependencies

# Install system dependencies required for PHP extensions and build tools
RUN apt-get update && apt-get install -y \
    build-essential \
    linux-libc-dev \
    $PHPIZE_DEPS \
    libpq-dev \
    libzip-dev \
    libicu-dev \
    zip \
    unzip \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install required PHP extensions for the application
RUN docker-php-ext-install pdo pdo_pgsql zip pcntl intl

# Install Redis extension for queue processing
RUN pecl install redis && docker-php-ext-enable redis

# Get latest Composer for dependency management
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

# Set working directory for the application
WORKDIR /app

# Copy application files
COPY . .

# Install production dependencies only
RUN composer install --no-dev --optimize-autoloader

# Stage 2: Production Runtime
# This stage creates the final production image with only runtime dependencies
FROM php:8.4-cli AS production

# Install only runtime dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    libzip-dev \
    libicu-dev \
    $PHPIZE_DEPS \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install required PHP extensions
RUN docker-php-ext-install pdo pdo_pgsql zip pcntl intl \
    && pecl install redis \
    && docker-php-ext-enable redis

# Set working directory
WORKDIR /app

# Copy application files
COPY . .

# Copy installed dependencies from build stage
COPY --from=build_dependencies --chown=www-data:www-data /app/vendor ./vendor

# Copy custom PHP configuration
COPY deployment/php.ini /usr/local/etc/php/conf.d/99-custom.ini

# Set proper permissions for Laravel storage and cache directories
RUN chown -R www-data:www-data /app/storage /app/bootstrap/cache

# Switch to non-root user for security
USER www-data

# The command will be specified in docker-compose.yml
# This allows for more flexible container usage (queue worker, scheduler, etc.)

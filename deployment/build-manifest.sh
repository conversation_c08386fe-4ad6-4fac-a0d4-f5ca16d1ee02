#!/bin/bash

# Multi-platform Docker build and manifest script
# This script builds Docker images for multiple platforms and creates manifests

set -e

# Configuration
IMAGE_NAME="your-org/laravel-app"
VERSION="${VERSION:-latest}"
PLATFORMS="linux/amd64,linux/arm64"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker Buildx is available
if ! docker buildx version >/dev/null 2>&1; then
    print_error "Docker Buildx is not available. Please install Docker Buildx."
    exit 1
fi

# Create and use a new builder instance for multi-platform builds
print_status "Setting up Docker Buildx builder..."
docker buildx create --name multiplatform-builder --use --driver docker-container || true

# Build and push the Octane application
print_status "Building and pushing Octane application for platforms: $PLATFORMS"
docker buildx build \
    --platform $PLATFORMS \
    --file deployment/octane.dockerfile \
    --tag $IMAGE_NAME:octane-$VERSION \
    --tag $IMAGE_NAME:octane-latest \
    --push \
    --build-arg VERSION=$VERSION \
    --build-arg GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown") \
    --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
    .

# Build and push the CLI application
print_status "Building and pushing CLI application for platforms: $PLATFORMS"
docker buildx build \
    --platform $PLATFORMS \
    --file deployment/cli.dockerfile \
    --tag $IMAGE_NAME:cli-$VERSION \
    --tag $IMAGE_NAME:cli-latest \
    --push \
    --build-arg VERSION=$VERSION \
    --build-arg GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown") \
    --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
    .

# Create and push manifests for easier image management
print_status "Creating and pushing manifests..."

# Create manifest for Octane
docker manifest create $IMAGE_NAME:octane \
    $IMAGE_NAME:octane-$VERSION

# Create manifest for CLI
docker manifest create $IMAGE_NAME:cli \
    $IMAGE_NAME:cli-$VERSION

# Push manifests
docker manifest push $IMAGE_NAME:octane
docker manifest push $IMAGE_NAME:cli

print_status "Build completed successfully!"
print_status "Available images:"
print_status "  - $IMAGE_NAME:octane (multi-platform)"
print_status "  - $IMAGE_NAME:cli (multi-platform)"
print_status "  - $IMAGE_NAME:octane-$VERSION"
print_status "  - $IMAGE_NAME:cli-$VERSION"

# Optional: Show manifest information
if command -v docker >/dev/null 2>&1; then
    print_status "Manifest information:"
    docker manifest inspect $IMAGE_NAME:octane | jq -r '.manifests[].platform' 2>/dev/null || echo "Unable to inspect manifest"
fi 
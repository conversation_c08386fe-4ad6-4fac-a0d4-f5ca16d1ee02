# Docker Manifests and Dynamic Metadata

This document explains how the Linanok Docker images use dynamic manifests and metadata for better image management and traceability.

## Overview

Both Dockerfiles (`deployment/cli.dockerfile` and `deployment/octane.dockerfile`) now include comprehensive metadata labels that are populated dynamically during the build process using build arguments.

**Image Types:**
- **Web Image** (`octane.dockerfile`) - FrankenPHP web server with Laravel Octane
- **CLI Image** (`cli.dockerfile`) - Laravel CLI container for artisan commands, queue workers, schedulers, etc.

> **Note:** The CLI image is versatile and can run any Laravel artisan command. In docker-compose, it's configured as a `queue-worker` service, but the same image can be used for other CLI tasks.

## Build Arguments

The following build arguments are used to populate the image metadata:

| Argument | Description | Default | Example |
|----------|-------------|---------|---------|
| `VERSION` | Application version | `dev` | `1.2.3`, `v2.0.0-beta` |
| `BUILD_DATE` | ISO 8601 build timestamp | None | `2025-07-31T10:30:00Z` |
| `VCS_REF` | Git commit hash | None | `abc123def456...` |
| `VCS_URL` | Repository URL | `https://github.com/linanok/linanok` | Custom repo URL |

## Metadata Labels

### OCI Standard Labels

The images include the following [OCI standard labels](https://github.com/opencontainers/image-spec/blob/main/annotations.md):

- `org.opencontainers.image.title` - Human-readable title
- `org.opencontainers.image.description` - Description of the image
- `org.opencontainers.image.version` - Version of the packaged software
- `org.opencontainers.image.created` - Date and time when the image was built
- `org.opencontainers.image.source` - URL to the source code repository
- `org.opencontainers.image.revision` - Source control revision identifier
- `org.opencontainers.image.licenses` - License(s) under which contained software is distributed
- `org.opencontainers.image.vendor` - Name of the distributing entity
- `org.opencontainers.image.authors` - Contact details of the people or organization responsible
- `org.opencontainers.image.documentation` - URL to find more information

### Application-Specific Labels

Custom labels for application-specific metadata:

- `app.name` - Application name (`linanok`)
- `app.component` - Component type (`web-server` or `cli`)
- `app.version` - Application version (same as OCI version)
- `app.framework` - Framework used (`laravel`)
- `app.php.version` - PHP version (`8.4`)
- `app.server` - Server type (`frankenphp` for web image only)
- `app.octane` - Octane enabled flag (`true` for web image only)
- `app.build.date` - Build timestamp
- `app.build.revision` - Git commit hash

## Usage Examples

### Local Development Build

```bash
# Basic build with default values
docker build -f deployment/octane.dockerfile -t linanok-web:dev .

# Build with custom version
docker build \
  --build-arg VERSION=1.2.3 \
  --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
  --build-arg VCS_REF=$(git rev-parse HEAD) \
  -f deployment/octane.dockerfile \
  -t linanok-web:1.2.3 .
```

### Using the Build Script

```bash
# Build dev images locally
./scripts/build-docker.sh

# Build specific version
./scripts/build-docker.sh -v 1.2.3

# Build and push to registry
./scripts/build-docker.sh -v 1.2.3 -p

# Using environment variables
VERSION=2.0.0 PUSH=true ./scripts/build-docker.sh
```

### Using Docker Compose (Local Development)

```bash
# Build with default values (dev)
docker-compose build

# Build with custom version
VERSION=1.2.3 BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') VCS_REF=$(git rev-parse HEAD) docker-compose build

# Or set in .env file
echo "VERSION=1.2.3" > .env
echo "BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> .env
echo "VCS_REF=$(git rev-parse HEAD)" >> .env
docker-compose build
```

### GitHub Actions

The GitHub Actions workflow (`.github/workflows/docker-build-push.yml`) automatically:

- Extracts version from git tags or branch names
- Sets build date to current timestamp
- Uses commit SHA as VCS reference
- Pushes to GitHub Container Registry
- Generates build summaries

### Docker Compose

Update your `docker-compose.yml` to use versioned images:

```yaml
services:
  web:
    image: ghcr.io/linanok/linanok-web:${VERSION:-latest}
    # ... rest of configuration

  queue-worker:
    image: ghcr.io/linanok/linanok-cli:${VERSION:-latest}
    # ... rest of configuration
```

## Inspecting Image Metadata

### View All Labels

```bash
# View all labels for an image
docker inspect linanok-web:1.2.3 | jq '.[0].Config.Labels'

# View specific OCI labels
docker inspect linanok-web:1.2.3 | jq '.[0].Config.Labels | with_entries(select(.key | startswith("org.opencontainers.image")))'

# View application-specific labels
docker inspect linanok-web:1.2.3 | jq '.[0].Config.Labels | with_entries(select(.key | startswith("app.")))'
```

### Using Docker CLI

```bash
# List images with custom format showing version and build date
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.CreatedAt}}"

# Filter images by label
docker images --filter "label=app.name=linanok"
```

## CI/CD Integration

### GitHub Actions Variables

The workflow uses these automatic variables:

- `github.sha` - Commit SHA for VCS_REF
- `github.server_url` and `github.repository` - For VCS_URL
- `docker/metadata-action` - For version extraction and BUILD_DATE

### Custom CI/CD Systems

For other CI/CD systems, set these environment variables:

```bash
export VERSION=${CI_COMMIT_TAG:-${CI_COMMIT_REF_NAME:-dev}}
export BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
export VCS_REF=${CI_COMMIT_SHA}
export VCS_URL=${CI_PROJECT_URL}
```

## Benefits

1. **Traceability**: Every image can be traced back to its source code and build
2. **Version Management**: Clear versioning for rollbacks and deployments
3. **Compliance**: Follows OCI standards for container metadata
4. **Automation**: Integrates seamlessly with CI/CD pipelines
5. **Debugging**: Build information helps with troubleshooting
6. **Registry Management**: Better organization in container registries

## Best Practices

1. Always use semantic versioning for releases
2. Include commit SHA for development builds
3. Set proper build dates for audit trails
4. Use consistent labeling across all images
5. Document any custom labels used
6. Validate metadata in CI/CD pipelines

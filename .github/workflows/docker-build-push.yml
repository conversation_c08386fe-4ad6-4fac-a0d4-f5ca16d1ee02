name: <PERSON><PERSON> and Push Docker Images

on:
  push:
    branches: [ main, demo ]
  workflow_call:
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  PROJECT: ${{ github.repository }}

jobs:
  test:
    permissions:
      contents: read
    uses: linanok/linanok/.github/workflows/test.yml@main

  build:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - dockerfile: deployment/octane.dockerfile
            image: octane
          - dockerfile: deployment/cli.dockerfile
            image: cli
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v4

      - name: Set Docker tags
        id: set_tags
        run: |
          IMAGE_PREFIX="${{ env.REGISTRY }}/${{ env.PROJECT }}/${{ matrix.image }}"
          echo "tags=${IMAGE_PREFIX}:unstable-${GITHUB_REF_NAME}" >> $GITHUB_OUTPUT

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ matrix.dockerfile }}
          push: true
          tags: |
            ${{ steps.set_tags.outputs.tags }}

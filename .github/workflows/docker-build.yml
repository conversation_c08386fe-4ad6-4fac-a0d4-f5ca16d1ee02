name: <PERSON><PERSON> and Push Docker Images

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_WEB: ${{ github.repository }}/web
  IMAGE_NAME_CLI: ${{ github.repository }}/cli

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata for Web image
      id: meta-web
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_WEB }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Extract metadata for CLI image
      id: meta-cli
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_CLI }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Web Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./deployment/octane.dockerfile
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta-web.outputs.tags }}
        labels: ${{ steps.meta-web.outputs.labels }}
        build-args: |
          VERSION=${{ steps.meta-web.outputs.version }}
          BUILD_DATE=${{ fromJSON(steps.meta-web.outputs.json).labels['org.opencontainers.image.created'] }}
          VCS_REF=${{ github.sha }}
          VCS_URL=${{ github.server_url }}/${{ github.repository }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push CLI Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./deployment/cli.dockerfile
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta-cli.outputs.tags }}
        labels: ${{ steps.meta-cli.outputs.labels }}
        build-args: |
          VERSION=${{ steps.meta-cli.outputs.version }}
          BUILD_DATE=${{ fromJSON(steps.meta-cli.outputs.json).labels['org.opencontainers.image.created'] }}
          VCS_REF=${{ github.sha }}
          VCS_URL=${{ github.server_url }}/${{ github.repository }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Generate build summary
      run: |
        echo "## Docker Images Built 🐳" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Web Application Image" >> $GITHUB_STEP_SUMMARY
        echo "- **Tags:** ${{ steps.meta-web.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Version:** ${{ steps.meta-web.outputs.version }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Digest:** ${{ steps.build-web.outputs.digest }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### CLI/Queue Worker Image" >> $GITHUB_STEP_SUMMARY
        echo "- **Tags:** ${{ steps.meta-cli.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Version:** ${{ steps.meta-cli.outputs.version }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Digest:** ${{ steps.build-cli.outputs.digest }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Build Information" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch/Tag:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Build Date:** $(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_STEP_SUMMARY

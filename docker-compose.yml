version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: deployment/octane.dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./:/app:cached
      - frankenphp-data:/tmp
    env_file:
      - .env.docker
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  queue-worker:
    build:
      context: .
      dockerfile: deployment/cli.dockerfile
    command: ["php", "artisan", "queue:work", "--tries=3", "--max-jobs=1000"]
    env_file:
      - .env.docker
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app
    deploy:
      replicas: ${QUEUE_WORKER_REPLICAS:-4}

  postgres:
    image: postgres:17-alpine
    volumes:
      - postgres-data:/var/lib/postgresql/data
    env_file:
      - .env.docker
    environment:
      - POSTGRES_DB=${DB_DATABASE}
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}" ]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app
    restart: unless-stopped

  redis:
    image: redis:8-alpine
    volumes:
      - redis-data:/data
    env_file:
      - .env.docker
    command: redis-server --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: [ "CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping" ]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app
    restart: unless-stopped

volumes:
  postgres-data:
  redis-data:
  frankenphp-data:

networks:
  app:
    driver: bridge

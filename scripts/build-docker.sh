#!/bin/bash

# Local build script for Linanok Docker images with dynamic metadata
# Useful for local development, testing, and non-GitHub CI/CD systems

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
VERSION=${VERSION:-"dev"}
BUILD_DATE=${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
VCS_REF=${VCS_REF:-$(git rev-parse HEAD 2>/dev/null || echo "unknown")}
VCS_URL=${VCS_URL:-"https://github.com/linanok/linanok"}
REGISTRY=${REGISTRY:-"ghcr.io/linanok"}
PUSH=${PUSH:-false}

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -v, --version VERSION    Set the version tag (default: dev)"
    echo "  -p, --push              Push images to registry after building"
    echo "  -r, --registry REGISTRY Set the registry URL (default: ghcr.io/linanok)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  VERSION     Image version (default: dev)"
    echo "  BUILD_DATE  Build timestamp (default: current UTC time)"
    echo "  VCS_REF     Git commit hash (default: current HEAD)"
    echo "  VCS_URL     Repository URL (default: https://github.com/linanok/linanok)"
    echo "  REGISTRY    Container registry (default: ghcr.io/linanok)"
    echo "  PUSH        Push to registry (default: false)"
    echo ""
    echo "Examples:"
    echo "  $0                       # Build dev images locally"
    echo "  $0 -v 1.2.3             # Build version 1.2.3"
    echo "  $0 -v 1.2.3 -p          # Build and push version 1.2.3"
    echo "  VERSION=1.0.0 $0        # Build using env vars"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Print build information
print_info "Building Linanok Docker images:"
echo "  Version:    $VERSION"
echo "  Build Date: $BUILD_DATE"
echo "  VCS Ref:    $VCS_REF"
echo "  Registry:   $REGISTRY"
echo "  Push:       $PUSH"
echo ""

# Build arguments
BUILD_ARGS=(
    "--build-arg" "VERSION=$VERSION"
    "--build-arg" "BUILD_DATE=$BUILD_DATE"
    "--build-arg" "VCS_REF=$VCS_REF"
    "--build-arg" "VCS_URL=$VCS_URL"
)

# Build web application image
print_info "Building web application image..."
WEB_TAG="$REGISTRY/linanok-web:$VERSION"
docker build "${BUILD_ARGS[@]}" -f deployment/octane.dockerfile -t "$WEB_TAG" .
print_success "Built: $WEB_TAG"

# Build CLI image
print_info "Building CLI image..."
CLI_TAG="$REGISTRY/linanok-cli:$VERSION"
docker build "${BUILD_ARGS[@]}" -f deployment/cli.dockerfile -t "$CLI_TAG" .
print_success "Built: $CLI_TAG"

# Push images if requested
if [ "$PUSH" = true ]; then
    print_info "Pushing images to registry..."
    docker push "$WEB_TAG" && print_success "Pushed: $WEB_TAG"
    docker push "$CLI_TAG" && print_success "Pushed: $CLI_TAG"
fi

print_info "Build completed successfully!"
echo ""
echo "Built images:"
echo "  Web: $WEB_TAG"
echo "  CLI: $CLI_TAG"
echo ""
echo "To inspect image metadata:"
echo "  docker inspect $WEB_TAG | jq '.[0].Config.Labels'"
echo "  docker inspect $CLI_TAG | jq '.[0].Config.Labels'"

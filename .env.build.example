# Docker Build Environment Variables
# Copy this file to .env.build and customize for your build environment

# Application version - use semantic versioning for releases
# Examples: 1.0.0, 2.1.3-beta, dev, main, feature-branch
VERSION=dev

# Build timestamp - automatically set if not provided
# Format: ISO 8601 (YYYY-MM-DDTHH:MM:SSZ)
# BUILD_DATE=2025-07-31T10:30:00Z

# Git commit hash - automatically detected if not provided
# VCS_REF=abc123def456789...

# Repository URL - customize if using a fork
VCS_URL=https://github.com/linanok/linanok

# Container registry for pushing images
REGISTRY=ghcr.io/linanok

# Whether to push images after building (true/false)
PUSH=false

# Queue worker replicas for docker-compose
QUEUE_WORKER_REPLICAS=4
